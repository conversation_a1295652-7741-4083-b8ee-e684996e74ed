.container {
  display: flex;
  gap: 10px;

  .left {
    width: 395px;
  }

  .right {
    flex: 1;
  }

  .left {
    .tip {
      font-size: 10px;
      color: gray;
    }

    .red {
      color: red;
    }

    .imgContainer {
      background: #f4f6f9;
      border-radius: 5px;
      margin-top: 15px;

      div {
        padding: 10px;
        position: relative;

        img {

          width: 375px;
          position: absolute;
          top: -99999px;
        }

        canvas {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }
      }
    }
  }

  .right {
    .title {
      font-size: 12px;
      font-weight: bold;
    }

    .hotZone {
      margin-top: 10px;

      .title {
        font-size: 10px;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .inputContainer {
        position: relative;
        display: flex;

        .input {
          width: 100%;
        }

        i {
          cursor: pointer;
          margin-left: 8px;
          margin-top: 4px;
          font-size: 12px;
        }
      }

    }

    .clearBtn {
      margin-top: 15px;

      .tip {
        font-size: 10px;
        color: gray;
        margin-top: 5px;
      }
    }


  }

}

.footer {
  display: flex;
  justify-content: center;
  gap: 10px;
}
