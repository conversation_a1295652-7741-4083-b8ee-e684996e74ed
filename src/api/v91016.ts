import {
  Activity91016CreateOrUpdateRequest,
  Activity91016CreateOrUpdateResponse,
  Activity91016DataRequest,
  Activity91016OrderRequest,
  Activity91016OrderShowResponse,
  Activity91016PrizeStatResponse,
  Activity91016PrizeUserRequest,
  Activity91016SkuRequest,
  Activity91016SkuResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity91016DataResponse,
  IPageActivity91016PrizeUserResponse,
} from './types';


import { request as httpRequest } from 'ice';

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 创建活动
 * @request POST:/91016/createActivity
 */
export const createActivity = (
  request: Activity91016CreateOrUpdateRequest,
): Promise<Activity91016CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/91016/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 参与记录
 * @request POST:/91016/data/joinRecord
 */
export const dataJoinRecord = (request: Activity91016DataRequest): Promise<IPageActivity91016DataResponse> => {
  return httpRequest({
    url: '/91016/data/joinRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 参与记录导出
 * @request POST:/91016/data/joinRecord/export
 */
export const dataJoinRecordExport = (activity10110ActivityDataRequest: Activity91016DataRequest): Promise<void> => {
  return httpRequest({
    url: '/91016/data/joinRecord/export',
    method: 'post',
    data: activity10110ActivityDataRequest,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 上传人群包
 * @request POST:/91016/data/joinRecord/uploadPin
 */
export const dataJoinRecordUploadPin = (activity91016DataRequest: Activity91016DataRequest): Promise<void> => {
  return httpRequest({
    url: '/91016/data/joinRecord/uploadPin',
    method: 'post',
    data: activity91016DataRequest,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 订单详情
 * @request POST:/91016/data/orderDetail
 */
export const dataOrderDetail = (request: Activity91016OrderRequest): Promise<Activity91016OrderShowResponse> => {
  return httpRequest({
    url: '/91016/data/orderDetail',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 中奖记录
 * @request POST:/91016/data/prizeUserRecord
 */
export const dataPrizeUserRecord = (
  request: Activity91016PrizeUserRequest,
): Promise<IPageActivity91016PrizeUserResponse> => {
  return httpRequest({
    url: '/91016/data/prizeUserRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 中奖记录导出
 * @request POST:/91016/data/prizeUserRecord/export
 */
export const dataPrizeUserRecordExport = (request: Activity91016PrizeUserRequest): Promise<void> => {
  return httpRequest({
    url: '/91016/data/prizeUserRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 上传人群包
 * @request POST:/91016/data/prizeUserRecord/uploadPin
 */
export const dataPrizeUserRecordUploadPin = (
  activity91016PrizeUserRequest: Activity91016PrizeUserRequest,
): Promise<void> => {
  return httpRequest({
    url: '/91016/data/prizeUserRecord/uploadPin',
    method: 'post',
    data: activity91016PrizeUserRequest,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary sku详情
 * @request POST:/91016/data/skuDetail
 */
export const dataSkuDetail = (request: Activity91016SkuRequest): Promise<Activity91016SkuResponse[]> => {
  return httpRequest({
    url: '/91016/data/skuDetail',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 查询活动信息
 * @request POST:/91016/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/91016/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 查询活动奖品信息
 * @request POST:/91016/getPrizeList
 */
export const getPrizeList = (request: BaseGetActivityRequest): Promise<Activity91016PrizeStatResponse[]> => {
  return httpRequest({
    url: '/91016/getPrizeList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags babyCare满额有礼(非券包)
 * @summary 修改活动
 * @request POST:/91016/updateActivity
 */
export const updateActivity = (
  request: Activity91016CreateOrUpdateRequest,
): Promise<Activity91016CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/91016/updateActivity',
    method: 'post',
    data: request,
  });
};
